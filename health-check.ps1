# MoneyTalk v2 Health Check Script
# Usage: .\health-check.ps1

Write-Host "🏥 MoneyTalk v2 Infrastructure Health Check" -ForegroundColor Cyan
Write-Host "=" * 50

# 1. Container Status Check
Write-Host "`n📦 Container Status:" -ForegroundColor Yellow
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Where-Object { $_ -match "mtv2-" }

# 2. Resource Usage
Write-Host "`n📊 Resource Usage:" -ForegroundColor Yellow  
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" | Where-Object { $_ -match "mtv2-" }

# 3. Health Endpoint Test
Write-Host "`n🩺 Application Health:" -ForegroundColor Yellow
try {
    $healthResponse = docker exec mtv2-gateway curl -s http://localhost:8080/actuator/health 2>$null
    $health = $healthResponse | ConvertFrom-Json
    
    Write-Host "Overall Status: " -NoNewline
    if ($health.status -eq "UP") {
        Write-Host "✅ UP" -ForegroundColor Green
    } else {
        Write-Host "❌ $($health.status)" -ForegroundColor Red
    }
    
    # Check individual services
    Write-Host "`nService Status:"
    foreach ($component in $health.components.PSObject.Properties) {
        $name = $component.Name
        $status = $component.Value.status
        
        if ($name -match "service|gateway") {
            Write-Host "  $name`: " -NoNewline
            if ($status -eq "UP") {
                Write-Host "✅ UP" -ForegroundColor Green
            } else {
                Write-Host "❌ $status" -ForegroundColor Red
            }
        }
    }
    
    # Check circuit breakers
    if ($health.components.circuitBreakers) {
        Write-Host "`nCircuit Breakers:"
        foreach ($cb in $health.components.circuitBreakers.details.PSObject.Properties) {
            $cbName = $cb.Name
            $cbState = $cb.Value.details.state
            Write-Host "  $cbName`: " -NoNewline
            if ($cbState -eq "CLOSED") {
                Write-Host "✅ CLOSED" -ForegroundColor Green
            } else {
                Write-Host "⚠️ $cbState" -ForegroundColor Yellow
            }
        }
    }
    
} catch {
    Write-Host "❌ Failed to get health status" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Recent Error Check
Write-Host "`n🔍 Recent Errors (last 5 minutes):" -ForegroundColor Yellow
$services = @("mtv2-auth", "mtv2-user", "mtv2-gateway", "mtv2-mysql")
$hasErrors = $false

foreach ($service in $services) {
    $errors = docker logs $service --since 5m 2>$null | Select-String -Pattern "ERROR|FATAL|Exception" | Select-Object -First 3
    if ($errors) {
        Write-Host "  $service`:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "    ❌ $($error.Line.Substring(0, [Math]::Min(100, $error.Line.Length)))" -ForegroundColor Red
        }
        $hasErrors = $true
    }
}

if (-not $hasErrors) {
    Write-Host "  ✅ No recent errors found" -ForegroundColor Green
}

# 5. Database Connectivity Test
Write-Host "`n🗄️ Database Connectivity:" -ForegroundColor Yellow
try {
    $dbTest = docker exec mtv2-mysql mysqladmin -u mtv2 -pmtv21234 ping 2>$null
    if ($dbTest -match "alive") {
        Write-Host "  ✅ MySQL is responding" -ForegroundColor Green
    } else {
        Write-Host "  ❌ MySQL connection failed" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ Database test failed" -ForegroundColor Red
}

# 6. Summary
Write-Host "`n📋 Summary:" -ForegroundColor Cyan
Write-Host "Run this script regularly to monitor your infrastructure health."
Write-Host "For detailed logs, use: docker logs <container-name> --tail 50"
Write-Host "For real-time monitoring, use: docker logs <container-name> -f"
