# MoneyTalk v2 Infrastructure Troubleshooting Guide

## 🚨 Common Issues and Solutions

### 1. Database Connection Issues

#### Symptoms
- Services show "Access denied for user 'mtv2'@'%' to database" errors
- "Unknown database" errors in service logs
- Health checks fail with database timeout errors
- Services stuck in unhealthy state

#### Root Cause
Database name mismatch between MySQL initialization script and Spring Boot configuration.

#### Diagnosis Steps
```bash
# 1. Check service logs for database errors
docker logs mtv2-auth --tail 20
docker logs mtv2-user --tail 20

# 2. Verify databases exist in MySQL
docker exec -it mtv2-mysql mysql -u mtv2 -pmtv21234 -e "SHOW DATABASES;"

# 3. Check user permissions
docker exec -it mtv2-mysql mysql -u mtv2 -pmtv21234 -e "SHOW GRANTS FOR 'mtv2'@'%';"

# 4. Test database connectivity
docker exec -it mtv2-mysql mysql -u mtv2 -pmtv21234 -e "SELECT 1;"
```

#### Solution
1. Ensure database names in `docker-compose.yml` match those in `mysql/init/00_init.sql`
2. Current correct configuration:
   - MySQL creates: `mtv2_authdb`, `mtv2_userdb`
   - Services connect to: `mtv2_authdb`, `mtv2_userdb`
3. If changes are made, recreate MySQL volume:
   ```bash
   docker-compose down
   docker volume rm moneytalk-v2_dbdata
   docker-compose up -d
   ```

### 2. Service Startup Issues

#### Symptoms
- Containers exit immediately after startup
- Services stuck in "Starting" state
- Port binding errors
- Dependency services not ready

#### Diagnosis Steps
```bash
# Check container status
docker ps -a

# Check service logs
docker-compose logs mysql
docker-compose logs auth
docker-compose logs user
docker-compose logs gateway

# Check port usage
netstat -an | findstr :8080
netstat -an | findstr :8100
netstat -an | findstr :8101
```

#### Solutions
- **MySQL not ready**: Ensure MySQL health check passes before starting dependent services
- **Port conflicts**: Change ports in docker-compose.yml or stop conflicting services
- **Missing environment variables**: Verify all required env vars are set in .env file
- **Image issues**: Rebuild images if needed: `docker-compose build --no-cache`

### 3. Health Check Failures

#### Symptoms
- Gateway shows services as DOWN in `/actuator/health`
- Timeout errors in health responses
- Circuit breakers opening (state: OPEN)
- Services unreachable from gateway

#### Diagnosis Steps
```bash
# Test individual service health endpoints
curl http://localhost:8100/actuator/health  # auth service
curl http://localhost:8101/actuator/health  # user service
curl http://localhost:8080/actuator/health  # gateway

# Test internal network connectivity
docker exec mtv2-gateway ping auth
docker exec mtv2-gateway ping user
docker exec mtv2-gateway ping mysql

# Check service response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8100/actuator/health
```

#### Solutions
- **Network issues**: Verify all services are on the same Docker network
- **Service not ready**: Wait for services to fully initialize (30-60 seconds)
- **Health check timeouts**: Adjust timeout settings in gateway configuration
- **Circuit breaker issues**: Reset by restarting the gateway service

### 4. Performance Issues

#### Symptoms
- Slow response times
- High memory usage
- Services becoming unresponsive
- Database connection pool exhaustion

#### Diagnosis Steps
```bash
# Monitor resource usage
docker stats

# Check memory usage in health endpoint
curl http://localhost:8080/actuator/health | grep memory

# Monitor database connections
docker exec -it mtv2-mysql mysql -u mtv2 -pmtv21234 -e "SHOW PROCESSLIST;"
```

#### Solutions
- **Memory issues**: Increase JVM heap size in docker-compose.yml
- **Connection pool**: Adjust database connection pool settings
- **Resource limits**: Set appropriate CPU/memory limits in docker-compose.yml

## 🛠 Quick Recovery Commands

### Full System Restart
```bash
# Complete restart with clean database
docker-compose down
docker volume rm moneytalk-v2_dbdata
docker-compose up -d

# Wait for services to start, then test
sleep 30
curl http://localhost:8080/actuator/health
```

### Partial Restart
```bash
# Restart specific service
docker-compose restart auth
docker-compose restart user
docker-compose restart gateway

# Restart without recreating volumes
docker-compose down
docker-compose up -d
```

### Emergency Debugging
```bash
# View real-time logs from all services
docker-compose logs -f

# Access service container for debugging
docker exec -it mtv2-auth bash
docker exec -it mtv2-user bash
docker exec -it mtv2-gateway bash

# Access MySQL for database inspection
docker exec -it mtv2-mysql mysql -u mtv2 -pmtv21234
```

## 📊 Monitoring and Maintenance

### Health Check Commands
```bash
# Overall system health
curl -s http://localhost:8080/actuator/health | jq .

# Individual service health
curl -s http://localhost:8100/actuator/health | jq .status
curl -s http://localhost:8101/actuator/health | jq .status

# Database connectivity test
docker exec mtv2-mysql mysqladmin -u mtv2 -pmtv21234 ping
```

### Log Monitoring
```bash
# Recent logs (last 50 lines)
docker logs mtv2-auth --tail 50
docker logs mtv2-user --tail 50
docker logs mtv2-gateway --tail 50
docker logs mtv2-mysql --tail 50

# Follow logs in real-time
docker logs mtv2-auth -f
docker logs mtv2-user -f
```

### System Status
```bash
# Container status
docker ps -a

# Volume usage
docker volume ls
docker system df

# Network information
docker network ls
docker network inspect moneytalk-v2_backend
```

## 🔧 Configuration Files to Check

When troubleshooting, verify these key configuration files:

1. **docker-compose.yml** - Service definitions and environment variables
2. **mysql/init/00_init.sql** - Database initialization script
3. **.env** - Environment variables
4. **Application properties** in each service (if accessible)

## 📞 Escalation Checklist

Before escalating issues, ensure you have:

- [ ] Checked all service logs for error messages
- [ ] Verified database connectivity and permissions
- [ ] Tested individual service health endpoints
- [ ] Confirmed network connectivity between services
- [ ] Attempted a full system restart
- [ ] Documented the exact error messages and steps to reproduce

## 🎯 Prevention Tips

- Always check database name consistency between init scripts and service configs
- Use health checks with appropriate timeouts
- Monitor resource usage regularly
- Keep service logs for debugging
- Test configuration changes in development first
- Document any custom configurations or modifications
