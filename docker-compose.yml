version: "3.8"

name: moneytalk-v2
services:
  mysql:
    image: mysql:8.0
    container_name: mtv2-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      TZ: Asia/Seoul
    command: [
      "--default-authentication-plugin=caching_sha2_password",
      "--character-set-server=utf8mb4",
      "--collation-server=utf8mb4_unicode_ci"
    ]
    volumes:
      - dbdata:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "mysqladmin ping -h localhost -uroot -p$$MYSQL_ROOT_PASSWORD | grep 'mysqld is alive'"]
      interval: 10s
      timeout: 5s
      retries: 10
    networks: [backend]
    # 호스트 포트 미공개(로컬 3306 충돌 방지)

  gateway:
    build:
      context: ../moneytalk-v2-gateway
    container_name: mtv2-gateway
    ports:
      - "8080:8080"
    depends_on:
      mysql:
        condition: service_healthy
      auth:
        condition: service_started
      user:
        condition: service_started
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    networks: [backend]

  auth:
    build:
      context: ../moneytalk-v2-auth
    container_name: mtv2-auth
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=${DB_APP_USER}
      - SPRING_DATASOURCE_PASSWORD=${DB_APP_PASSWORD}
    depends_on:
      mysql:
        condition: service_healthy
    networks: [backend]

  user:
    build:
      context: ../moneytalk-v2-user
    container_name: mtv2-user
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=${DB_APP_USER}
      - SPRING_DATASOURCE_PASSWORD=${DB_APP_PASSWORD}
    depends_on:
      mysql:
        condition: service_healthy
    networks: [backend]

volumes:
  dbdata:

networks:
  backend:
    driver: bridge
